plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'com.google.dagger.hilt.android'
    id 'org.jetbrains.kotlin.plugin.compose'
}

android {
    namespace 'com.robin.async.rpc'
    compileSdk 36

    defaultConfig {
        applicationId "com.robin.async.rpc"
        minSdk 26
        targetSdk 36
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        viewBinding true
    }

    sourceSets {
        main {
            assets.srcDirs = ['src/main/assets']
        }
    }
    aaptOptions {
        noCompress 'xposed_init'
    }
}

dependencies {
    implementation libs.androidbrowserhelper

    // Android core
    implementation libs.androidx.core.ktx.v1120
    implementation libs.androidx.appcompat
    implementation libs.material
    implementation libs.androidx.lifecycle.runtime.ktx.v270

    // Compose
    implementation platform(libs.androidx.compose.bom)
    implementation libs.androidx.ui
    implementation libs.material3
    implementation libs.androidx.ui.tooling.preview
    implementation libs.androidx.activity.compose.v182
    implementation libs.androidx.navigation.compose

    // Room
    implementation libs.androidx.room.runtime
    implementation libs.androidx.room.ktx

    // noinspection KaptUsageInsteadOfKsp
    kapt libs.androidx.room.compiler

    // Hilt
    implementation libs.hilt.android
    kapt libs.hilt.compiler
    implementation libs.androidx.hilt.navigation.compose

    implementation(libs.hutool.all)

    // Retrofit
    implementation libs.retrofit
    implementation libs.converter.gson

    // Hook
    compileOnly(libs.sekiro.business.api)
    compileOnly libs.xposed.v82

    // Testing
    debugImplementation libs.androidx.ui.tooling
    testImplementation libs.junit
    androidTestImplementation libs.androidx.junit.v121
    androidTestImplementation libs.androidx.ui.test.junit4

    implementation(libs.rhino.android)
}