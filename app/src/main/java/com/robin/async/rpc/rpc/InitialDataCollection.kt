package com.robin.async.rpc.rpc

import de.robv.android.xposed.IXposedHookLoadPackage
import de.robv.android.xposed.XC_MethodHook
import de.robv.android.xposed.XposedBridge
import de.robv.android.xposed.XposedHelpers
import de.robv.android.xposed.callbacks.XC_LoadPackage
import org.json.JSONObject
import java.lang.Long
import kotlin.Any
import kotlin.String
import kotlin.arrayOf

class InitialDataCollection : IXposedHookLoadPackage {

    override fun handleLoadPackage(lpparam: XC_LoadPackage.LoadPackageParam) {
        XposedBridge.log("===Initial Data Collection Start!===")
        val packageName = lpparam.packageName
        val processName = lpparam.processName
        XposedBridge.log("===packageName : $packageName===")
        XposedBridge.log("===processName : $processName===")
        if ((packageName == "com.tencent.mm")) {
            val lClazz = lpparam.classLoader.loadClass("com.tencent.mm.plugin.appbrand.jsapi.l")
            val nClazz = lpparam.classLoader.loadClass("g71.n")
            XposedHelpers.findAndHookMethod(
                lpparam.classLoader.loadClass("g71.q"),
                "g",
                *arrayOf<Any?>(
                    lClazz,
                    Integer.TYPE,
                    JSONObject::class.java,
                    java.util.Map::class.java,
                    java.util.ArrayList::class.java,
                    nClazz,
                    String::class.java,
                    String::class.java,
                    object : XC_MethodHook() {

                        override fun beforeHookedMethod(param: MethodHookParam) {
                            XposedBridge.log("g params : ${param.args.joinToString(",")}")
                            val jsonObject = param.args[2] as JSONObject
                            val url = jsonObject.get("url")
                            val data = jsonObject.get("data")
                            super.beforeHookedMethod(param)
                        }

                        override fun afterHookedMethod(param: MethodHookParam?) {
                            super.afterHookedMethod(param)
                        }
                    })
            )

            XposedHelpers.findAndHookMethod(
                lpparam.classLoader.loadClass("com.tencent.mm.appbrand.commonjni.AppBrandJsBridgeBinding"),
                "nativeInvokeCallbackHandler",
                *arrayOf<Any?>(
                    Long.TYPE,
                    Integer.TYPE,
                    String::class.java,
                    String::class.java,
                    object : XC_MethodHook() {

                        override fun beforeHookedMethod(param: MethodHookParam) {
                            XposedBridge.log("nativeInvokeCallbackHandler params : ${param.args.joinToString(",")}")
                            super.beforeHookedMethod(param)
                        }

                        override fun afterHookedMethod(param: MethodHookParam?) {
                            super.afterHookedMethod(param)
                        }
                    })
            )
        }



    }
}