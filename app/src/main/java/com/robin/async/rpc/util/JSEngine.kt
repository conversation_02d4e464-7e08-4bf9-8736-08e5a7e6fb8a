package com.robin.async.rpc.util

import org.mozilla.javascript.Context
import org.mozilla.javascript.Scriptable
import org.mozilla.javascript.Function

class JSEngine {
    // 初始化Rhino JS引擎
    private val context: Context = Context.enter()
    private val scope: Scriptable

    init {
        context.setOptimizationLevel(-1)
        scope = context.initStandardObjects()
    }

    fun loadScript(script: String?) {
        try {
            context.evaluateString(scope, script, "script", 1, null)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun callFunction(functionName: String?, vararg args: Any?): Any? {
        var cx: Context? = null
        try {
            cx = Context.getCurrentContext()
            if (cx == null) {
                cx = Context.enter()
            }
            val function = scope.get(functionName, scope)
            if (function is Function) {
                return function.call(cx, scope, scope, args)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            // 只有当前线程 enter 过才 exit
            if (cx != null) {
                Context.exit()
            }
        }
        return null
    }

    fun release() {
        Context.exit()
    }
}