package com.robin.async.rpc.rpc.ibox;

import com.virjar.sekiro.business.api.interfaze.Action;
import com.virjar.sekiro.business.api.interfaze.AutoBind;
import com.virjar.sekiro.business.api.interfaze.RequestHandler;
import com.virjar.sekiro.business.api.interfaze.SekiroRequest;
import com.virjar.sekiro.business.api.interfaze.SekiroResponse;

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONUtil;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;

@Action("nft18AppCall")
/* loaded from: classes3.dex */
public class Nft18SekiroHandler implements RequestHandler {

    @AutoBind
    private String base64Params;
    private Object targetObj;

    public Nft18SekiroHandler(Object targetObj) {
        this.targetObj = targetObj;
    }

    @Override // com.virjar.sekiro.business.api.interfaze.RequestHandler
    public void handleRequest(SekiroRequest sekiroRequest, SekiroResponse sekiroResponse) {
        String params = Base64.decodeStr(this.base64Params);
        XposedBridge.log("nft18 param  => " + params);
        IBoxBody iBoxBody = (IBoxBody) JSONUtil.toBean(params, IBoxBody.class);
        System.out.println("");
        Object o = XposedHelpers.callMethod(this.targetObj, "SyncCallContainer", new Object[]{iBoxBody.url, iBoxBody.method, iBoxBody.headers, iBoxBody.body});
        XposedBridge.log("nft18 callRes  => " + JSONUtil.toJsonStr(o));
        sekiroResponse.success(JSONUtil.parseObj(o).getStr("data"));
    }
}