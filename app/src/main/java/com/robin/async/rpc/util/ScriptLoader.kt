package com.robin.async.rpc.util

import android.content.Context
import java.io.BufferedReader
import java.io.IOException
import java.io.InputStreamReader
import java.nio.charset.StandardCharsets


object ScriptLoader {
    @Throws(IOException::class)
    fun loadFromAssets(context: Context, filename: String): String {
        val stringBuilder = StringBuilder()
        context.assets.open(filename).use { inputStream ->
            BufferedReader(
                InputStreamReader(inputStream, StandardCharsets.UTF_8)
            ).use { reader ->
                var line: String?
                while ((reader.readLine().also { line = it }) != null) {
                    stringBuilder.append(line)
                    stringBuilder.append("\n")
                }
            }
        }
        return stringBuilder.toString()
    }

    fun validateScript(script: String?): <PERSON><PERSON><PERSON> {
        // 添加基本的脚本验证逻辑
        return script != null && !script.isEmpty()
    }
}