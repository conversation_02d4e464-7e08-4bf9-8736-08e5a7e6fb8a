import com.robin.async.rpc.util.JSEngine
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException

class NetworkInterceptor(private val jsEngine: JSEngine) : Interceptor {
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()


        // 调用JS函数处理请求
        jsEngine.callFunction("onRequest", request)

        val response = chain.proceed(request)

        // 调用JS函数处理响应
        jsEngine.callFunction("onResponse", response)

        return response
    }
}